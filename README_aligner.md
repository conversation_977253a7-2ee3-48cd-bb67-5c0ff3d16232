# Audio Track Aligner OTIO Generator

This is a new version of the main OTIO generator that uses audio track alignment instead of silence/black frame detection to create cut segments.

## Key Differences from Original main.py

### Original Approach (main.py)
- Uses **silence detection** or **black frame detection** to find cut points
- Detects gaps/breaks in audio or video to determine where to split content
- Works well when there are clear silence periods or black frames between segments

### New Approach (main_aligner.py)
- Uses **audio fingerprinting and alignment** to match segments between English and Dutch tracks
- Analyzes audio features (spectral contrast, RMS energy, zero crossing rate, etc.) to find matching content
- Creates segment mappings based on audio similarity rather than gaps
- More sophisticated approach that can handle content without clear breaks

## How It Works

1. **Audio Extraction**: Extracts audio from both English (original) and Dutch (dub) video files
2. **Fingerprint Generation**: Creates audio fingerprints for chunks of audio using:
   - Spectral contrast (good for music/effects detection)
   - RMS energy (overall energy levels)
   - Zero crossing rate (helps distinguish music from speech)
   - Spectral bandwidth and rolloff
3. **Matching**: Finds matching points between English and Dutch audio using cosine similarity
4. **Segment Mapping**: Creates time-aligned segments between the two tracks
5. **OTIO Generation**: Converts segment mappings to OTIO cuts and creates timeline

## Usage

```bash
# Basic usage (reads directory from config.yml)
python main_aligner.py

# Specify directory
python main_aligner.py --dir "C:/path/to/your/videos"

# With re-encoding options
python main_aligner.py --reencode

# With FPS conversion
python main_aligner.py --dub-convert
```

## Output

- Creates OTIO files with `_aligned.otio` suffix to distinguish from original method
- Maintains the same track structure as original:
  - Original video track
  - Dub video track (with Transform effect for picture-in-picture)
  - Original audio track
  - Dub audio track
- Preserves DaVinci Resolve linking metadata

## When to Use Each Approach

### Use Original main.py when:
- Your content has clear silence periods or black frames between segments
- You want to split based on actual gaps in content
- Processing content with natural breaks (commercials, scene changes, etc.)

### Use main_aligner.py when:
- Your content doesn't have clear silence/black frame breaks
- You want to align similar content between different language versions
- The English and Dutch versions have different timing but similar content structure
- You need more sophisticated content-based alignment

## Requirements

Both scripts use the same dependencies, but the aligner version additionally leverages:
- `librosa` for audio analysis
- `numpy` and `scipy` for signal processing
- The existing `audio_track_aligner.py` module

## Technical Details

### Segment Conversion
The aligner converts `SegmentMapping` objects to cut dictionaries:

```python
SegmentMapping(
    english_start=10.5,    # seconds
    english_end=25.3,      # seconds  
    dutch_start=12.1,      # seconds
    dutch_end=27.8         # seconds
)
```

Becomes cut dictionaries with frame-based timing:
```python
{
    "cut_in": 252,         # frames (10.5 * 24fps)
    "cut_out": 607,        # frames (25.3 * 24fps)
    "cut_duration": 355,   # frames
    "link_id": 1
}
```

### Linking System
- Maintains the same Link Group ID system for DaVinci Resolve
- Each segment gets a unique link ID shared between video and audio clips
- Dub tracks get Transform effects for picture-in-picture display

## Troubleshooting

### "Insufficient matching points found"
- The audio tracks are too different to align reliably
- Try adjusting the `match_threshold` in the aligner (default 0.8)
- Check that both files contain similar content

### "Failed to create segment map"
- Not enough valid segments found after filtering
- May need to adjust `min_segment_duration` parameter

### Quality Issues
- The aligner preserves original video quality by using stream copying
- Audio alignment may introduce slight timing adjustments
